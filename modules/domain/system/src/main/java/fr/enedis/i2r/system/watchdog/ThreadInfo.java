package fr.enedis.i2r.system.watchdog;

import java.time.Instant;

public class ThreadInfo {
    private final Thread thread;
    private volatile Instant lastHeartbeat;

    ThreadInfo(Thread thread, Instant lastHeartbeat) {
        this.thread = thread;
        this.lastHeartbeat = lastHeartbeat;
    }

    public Thread getThread() {
        return thread;
    }

    public Instant getLastHeartbeat() {
        return lastHeartbeat;
    }

    public void setLastHeartbeat(Instant lastHeartbeat) {
        this.lastHeartbeat = lastHeartbeat;
    }
}
