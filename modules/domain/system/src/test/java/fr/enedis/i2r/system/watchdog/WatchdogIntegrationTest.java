package fr.enedis.i2r.system.watchdog;

import static org.junit.jupiter.api.Assertions.assertDoesNotThrow;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import java.lang.management.ManagementFactory;
import java.lang.management.ThreadInfo;
import java.lang.management.ThreadMXBean;
import java.time.Duration;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.TimeUnit;

import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.Timeout;
import org.mockito.ArgumentCaptor;

class WatchdogIntegrationTest {

    private DeadlockDetector deadlockDetector;
    private ThreadWatchdog threadWatchdog;
    private DeadlockHandler handler;

    @BeforeEach
    void setUp() {
        threadWatchdog = new ThreadWatchdog();
        handler = mock(DeadlockHandler.class);
        deadlockDetector = new DeadlockDetector(
            ManagementFactory.getThreadMXBean(),
            handler
        );
    }

    @AfterEach
    void tearDown() {
        threadWatchdog.shutdown();
    }

    @Test
    @Timeout(15)
    void la_detection_des_deadlocks_fonctionne_avec_plusieurs_threads() throws Exception {
        threadWatchdog.setHeartbeatInterval(Duration.ofSeconds(1));

        String[] threadNames = {"worker-1", "worker-2", "worker-3"};
        for (String name : threadNames) {
            assertDoesNotThrow(() -> threadWatchdog.register(name));
        }

        // Simulate real workload with deadlock detection; no deadlock expected
        for (int i = 0; i < 5; i++) {
            deadlockDetector.checkAndHandleDeadlocks();
            Thread.sleep(200);
        }

        verify(handler, never()).handle(any());
    }

    @Test
    @Timeout(20)
    void la_detection_des_deadlocks_fonctionne_en_parallele_que_la_surveillance_des_threads() throws Exception {
        threadWatchdog.setHeartbeatInterval(Duration.ofSeconds(1));
        assertDoesNotThrow(() -> threadWatchdog.register("monitor-thread-1"));
        assertDoesNotThrow(() -> threadWatchdog.register("monitor-thread-2"));

        // Mock to simulate deadlock detection
        ThreadMXBean mockThreadBean = mock(ThreadMXBean.class);
        long[] deadlockedThreadIds = {7L, 8L};
        ThreadInfo mockThreadInfo1 = mock(ThreadInfo.class);
        ThreadInfo mockThreadInfo2 = mock(ThreadInfo.class);

        when(mockThreadBean.findDeadlockedThreads()).thenReturn(deadlockedThreadIds);
        when(mockThreadBean.getThreadInfo(deadlockedThreadIds)).thenReturn(new ThreadInfo[] { mockThreadInfo1, mockThreadInfo2 });

        DeadlockHandler customHandler = mock(DeadlockHandler.class);
        DeadlockDetector mockDeadlockDetector = new DeadlockDetector(mockThreadBean, customHandler);

        mockDeadlockDetector.checkAndHandleDeadlocks();

        // Deadlock handler should be called
        ArgumentCaptor<ThreadInfo[]> captor = ArgumentCaptor.forClass(ThreadInfo[].class);
        verify(customHandler, times(1)).handle(captor.capture());
        ThreadInfo[] infos = captor.getValue();
        assertNotNull(infos);
        assertEquals(2, infos.length);
        assertDoesNotThrow(() -> threadWatchdog.register("additional-thread"));
    }

    @Test
    @Timeout(10)
    void le_changement_de_configuration_pendant_la_sureveillance_des_threads_est_possible() throws Exception {
        String threadName1 = "config-change-thread-1";
        String threadName2 = "config-change-thread-2";

        threadWatchdog.setHeartbeatInterval(Duration.ofSeconds(2));
        assertDoesNotThrow(() -> threadWatchdog.register(threadName1));

        Thread.sleep(1000);

        // Change interval
        threadWatchdog.setHeartbeatInterval(Duration.ofSeconds(1));

        assertDoesNotThrow(() -> threadWatchdog.register(threadName2));
        Thread.sleep(1500);
        assertDoesNotThrow(() -> threadWatchdog.register("verification-thread"));
        verify(handler, never()).handle(any());
    }


    @Test
    @Timeout(10)
    void la_detection_des_deadlocks_et_la_surveillance_des_threads_fonctionnent_sous_conditions_de_stress() throws Exception {
        threadWatchdog.setHeartbeatInterval(Duration.ofMillis(500));

        CountDownLatch startLatch = new CountDownLatch(1);
        CountDownLatch doneLatch = new CountDownLatch(3);

        // Thread 1: Continuously register threads (ThreadWatchdog manages them automatically)
        Thread watchdogThread = new Thread(() -> {
            try {
                startLatch.await();
                for (int i = 0; i < 50; i++) {
                    String threadName = "stress-thread-" + i;
                    threadWatchdog.register(threadName);
                    Thread.sleep(20);
                }
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
            } finally {
                doneLatch.countDown();
            }
        });

        // Thread 2: Continuously check for deadlocks
        Thread deadlockThread = new Thread(() -> {
            try {
                startLatch.await();
                for (int i = 0; i < 100; i++) {
                    deadlockDetector.checkAndHandleDeadlocks();
                    Thread.sleep(5);
                }
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
            } finally {
                doneLatch.countDown();
            }
        });

        // Thread 3: Mixed operations
        Thread mixedThread = new Thread(() -> {
            try {
                startLatch.await();
                for (int i = 0; i < 30; i++) {
                    threadWatchdog.register("mixed-thread-" + i);
                    deadlockDetector.checkAndHandleDeadlocks();
                    Thread.sleep(25);
                }
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
            } finally {
                doneLatch.countDown();
            }
        });

        watchdogThread.start();
        deadlockThread.start();
        mixedThread.start();

        startLatch.countDown();

        // Wait for completion
        assertTrue(doneLatch.await(8, TimeUnit.SECONDS), "All stress test threads should complete");
        assertDoesNotThrow(() -> threadWatchdog.register("post-stress-verification"));
        verify(handler, never()).handle(any());
    }
}
